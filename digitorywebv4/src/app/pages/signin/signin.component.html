<div class="page-content">
  <app-title-card></app-title-card>
  <app-form-wrapper>
    <div class="header">
      <h2>Sign in</h2>
      <p>Let's make work simpler and faster</p>
    </div>
    <form class="form" [formGroup]="form">
      <mat-form-field class="form-field" appearance="outline">
        <mat-label>Tenant</mat-label>
        <input matInput formControlName="tenantId" />
      </mat-form-field>

      <mat-form-field class="form-field" appearance="outline">
        <mat-label>Email</mat-label>
        <input matInput formControlName="email" />
      </mat-form-field>

      <mat-form-field class="form-field" appearance="outline">
        <mat-label>Password</mat-label>
        <!-- <div class="input-group"> -->
          <input matInput formControlName="password" #passwordInput [type]="isPasswordVisible ? 'text' : 'password'"
             placeholder="Enter your password" />
          <!-- <div class="input-group-append">class="form-control" -->
            <!-- <button type="button" class="btn btn-outline-secondary" > -->
              <mat-icon matSuffix (click)="togglePasswordVisibility()">{{ isPasswordVisible ? 'visibility_off' : 'visibility' }}</mat-icon>
            <!-- </button> -->
          <!-- </div> -->
        <!-- </div> -->
        <!-- <mat-hint align="end">min length: {{ passwordInput.value.length }}/6</mat-hint> -->
      </mat-form-field>

      <div class="actions">
        <button mat-raised-button color="primary" (click)="resetForm()" type="button">Cancel</button>
        <button #submitbtn mat-raised-button [disabled]="form.invalid" color="accent" type="submit" (click)="submit()">
          <div *ngIf="load" class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
          </div>
          Sign in
        </button>
      </div>
    </form>
  </app-form-wrapper>

  <div class="copyright">
    Copyright © 2020-2024. Digitory Solutions Pvt Ltd
  </div>
</div>
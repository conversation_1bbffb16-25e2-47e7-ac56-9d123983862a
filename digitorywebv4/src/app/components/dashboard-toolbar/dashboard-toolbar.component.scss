mat-toolbar {
  background: linear-gradient(135deg, #ffffff 0%, #fff8f5 100%);
  color: #333;
  padding: 0 16px;
  height: 40px; /* Keep it lean */
  min-height: 40px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(255, 179, 102, 0.12);
  border-bottom: 1px solid rgba(255, 179, 102, 0.15);
  position: relative;
  z-index: 1000;
  margin-bottom: 8px; /* Add space below to differentiate from page content */
}

.toolbar-left {
  display: flex;
  align-items: center;
  flex: 1;
  padding-left: 0;
}

.nav-menu {
  display: flex;
  align-items: stretch; /* Changed to stretch for full height tabs */
  margin-left: 0;
  gap: 4px; /* Consistent gap between tabs */

  .nav-item {
    min-width: 150px; /* Fixed width for consistency */
    max-width: 170px;
    height: 40px; /* Match lean toolbar height */
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0;
    color: #555;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    margin: 0;
    padding: 0 12px;
    font-weight: 500;
    font-size: 13px;
    text-transform: none;
    letter-spacing: 0.25px;
    background: transparent;
    border: none;
    overflow: hidden;

    /* Remove default mat-button styling */
    ::ng-deep .mat-mdc-button-touch-target {
      height: 100%;
      width: 100%;
    }

    ::ng-deep .mdc-button__label {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
    }

    mat-icon {
      margin-right: 8px;
      color: #ffb366;
      font-size: 18px;
      width: 18px;
      height: 18px;
      transition: all 0.3s ease;
    }

    span {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      transition: color 0.3s ease;
    }

    /* Top indicator bar for active state */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 0;
      height: 3px;
      background: linear-gradient(90deg, #ffb366 0%, #ffa64d 100%);
      border-radius: 0 0 2px 2px;
      transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Hover effects */
    &:hover:not(.active):not(.placeholder-tab) {
      background: linear-gradient(135deg, rgba(255, 179, 102, 0.06) 0%, rgba(255, 179, 102, 0.08) 100%);
      color: #333;
      transform: translateY(-1px);

      mat-icon {
        color: #ffa64d;
        transform: scale(1.05);
      }

      &::before {
        width: 100%;
        background: linear-gradient(90deg, rgba(255, 179, 102, 0.4) 0%, rgba(255, 164, 77, 0.4) 100%);
      }
    }

    /* Active state */
    &.active {
      font-weight: 600;
      color: #333;
      background: linear-gradient(135deg, rgba(255, 179, 102, 0.08) 0%, rgba(255, 179, 102, 0.12) 100%);
      box-shadow: inset 0 -1px 0 rgba(255, 179, 102, 0.2);

      mat-icon {
        color: #ff9100;
        transform: scale(1.1);
      }

      /* Full width top indicator */
      &::before {
        width: 100%;
        height: 3px;
        background: linear-gradient(90deg, #ffb366 0%, #ffa64d 100%);
        box-shadow: 0 1px 3px rgba(255, 179, 102, 0.3);
      }
    }

    /* Placeholder tab styling */
    &.placeholder-tab {
      opacity: 0.6;
      cursor: default;
      pointer-events: none;
      background: linear-gradient(135deg, rgba(255, 179, 102, 0.02) 0%, rgba(255, 179, 102, 0.04) 100%);

      mat-icon {
        color: rgba(255, 179, 102, 0.4);
      }

      span {
        color: #999;
      }

      &::before {
        display: none;
      }
    }
  }
}

/* Spacer for right-aligned content */
.example-spacer {
  flex: 1 1 auto;
}

/* Right side content styling */
.icons {
  display: flex;
  align-items: center;
  gap: 24px;
}

/* Override Material toolbar defaults */
::ng-deep .mat-toolbar-row,
::ng-deep .mat-toolbar-single-row {
  padding: 0 !important;
  height: 40px !important;
  max-height: 40px !important;
}

/* User info section */
.user-info {
  display: flex;
  align-items: center;
  margin-left: 16px;
}

.user-menu-button {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 32px;
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 179, 102, 0.2);
  backdrop-filter: blur(10px);

  &:hover {
    background: rgba(255, 179, 102, 0.08);
    border-color: rgba(255, 179, 102, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 179, 102, 0.15);
  }

  mat-icon {
    margin-right: 12px;
    font-size: 24px;
    height: 24px;
    width: 24px;
    color: #ffb366;
    transition: all 0.3s ease;
  }

  &:hover mat-icon {
    color: #ffa64d;
    transform: scale(1.05);
  }
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  line-height: 1.3;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.user-role {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  opacity: 0.9;
}

// .menu_buttons {
//   min-width: auto !important;
//   line-height: 36px;
//   // padding: 0 !important;
//   // margin: 0 10px !important;
// }

// .menu_buttons2 {
//   margin: 0 24px !important;
// }

/* Version info styling */
.formal-text {
  font-size: 12px;
  color: #666;
  margin: 0 16px 0 0;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;

  .beta-tag {
    background: linear-gradient(135deg, #ffb366 0%, #ffa64d 100%);
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 9px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 1px 2px rgba(255, 179, 102, 0.3);
  }
}

/* Legacy beta-tag support */
.beta-tag {
  background: linear-gradient(135deg, #ffb366 0%, #ffa64d 100%);
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 9px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(255, 179, 102, 0.3);
  margin-left: 6px;
}

.mat-mdc-button>.mat-icon {
  overflow: visible !important;
}

.globalSelectFormField{
  ::ng-deep .mat-mdc-form-field-subscript-wrapper {
    display: none !important;
  }
}

.globalSelectFormField{
  ::ng-deep .mdc-text-field--filled:not(.mdc-text-field--disabled) {
    background-color: #ebebeb;
}
}

.globalSelectFormField{
  width: 215px;
  height: 45px;
  margin-bottom: 10px;
  margin-right: 5px;
}

.globalSelectInput{
  height: 30px;
  padding: 10px;
}

.mdc-text-field--no-label:not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mat-mdc-form-field-infix {
  padding-top: 14px;
  padding-bottom: 16px;
}

/* Responsive Design for Professional Tabs */
@media (max-width: 1200px) {
  .nav-menu .nav-item {
    min-width: 140px;
    max-width: 160px;
    padding: 0 10px;

    span {
      font-size: 12px;
    }
  }
}

@media (max-width: 992px) {
  .nav-menu .nav-item {
    min-width: 120px;
    max-width: 140px;
    padding: 0 8px;

    mat-icon {
      margin-right: 6px;
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    span {
      font-size: 11px;
    }
  }

  .formal-text {
    display: none; /* Hide version on smaller screens */
  }
}

@media (max-width: 768px) {
  mat-toolbar {
    padding: 0 16px;
    height: 40px;
    min-height: 40px;
  }

  ::ng-deep .mat-toolbar-row,
  ::ng-deep .mat-toolbar-single-row {
    height: 40px !important;
    max-height: 40px !important;
  }

  .nav-menu {
    gap: 2px;

    .nav-item {
      min-width: 120px;
      max-width: 140px;
      height: 40px;
      padding: 0 8px;

      mat-icon {
        margin-right: 6px;
        font-size: 16px;
        width: 16px;
        height: 16px;
      }

      span {
        font-size: 11px;
      }
    }
  }

  .user-menu-button {
    padding: 4px 8px;
    height: 28px;

    mat-icon {
      margin-right: 4px;
      font-size: 16px;
      height: 16px;
      width: 16px;
    }
  }

  .user-details {
    .user-name {
      font-size: 12px;
    }

    .user-role {
      font-size: 10px;
    }
  }
}
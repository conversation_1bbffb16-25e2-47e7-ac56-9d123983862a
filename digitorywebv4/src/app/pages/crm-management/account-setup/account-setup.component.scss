// Main container styles
.account-setup-container {
  padding: 16px;
  max-width: 1400px;
  margin: 0 auto;

  // Breadcrumbs styles
  .breadcrumbs {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 13px;
    background-color: #f5f7fa;
    padding: 8px 12px;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border: 1px solid #e0e4e8;

    .breadcrumb-item {
      color: #666;
      text-decoration: none;
      transition: color 0.2s ease;
      display: flex;
      align-items: center;

      .breadcrumb-icon {
        font-size: 18px;
        height: 18px;
        width: 18px;
        margin-right: 4px;
      }

      &:hover {
        color: #3f51b5;
        text-decoration: underline;
      }

      &.active {
        color: #3f51b5;
        font-weight: 500;
      }
    }

    .breadcrumb-separator {
      margin: 0 8px;
      color: #999;
    }

    // Responsive styles for breadcrumbs
    @media (max-width: 768px) {
      font-size: 12px;

      .breadcrumb-item {
        .breadcrumb-icon {
          font-size: 16px;
          height: 16px;
          width: 16px;
        }
      }

      .breadcrumb-separator {
        margin: 0 4px;
      }
    }
  }

  // Compact header
  .compact-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .header-actions {
      display: flex;
      gap: 8px;

      .save-button,
      .cancel-button {
        min-width: 100px;
      }
    }
  }

  // Content section
  .content-section {
    .form-card {
      margin-bottom: 16px;
      border-radius: 4px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

      mat-card-content {
        padding: 12px;
      }
    }

    // Form styles
    .account-form {
      .form-section-title {
        font-size: 16px;
        font-weight: 500;
        margin: 0 0 16px 0;
        color: #555;
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;
        text-align: center;
      }

      // Compact form grid
      .compact-form-grid {
        .form-row {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;
          margin-bottom: 12px;

          .form-field {
            flex: 1;
            min-width: 200px;
            margin-bottom: 0;
          }
        }

        // Settings section styles
        .settings-section {
          margin-top: 10px;

          .two-column-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 0;
            align-items: flex-start;
            justify-content: space-between;
            padding: 0;
            width: 100%;
            margin: 20px 0 0;

            .left-column {
              flex: 1;
              min-width: 250px;
              padding-right: 0;
              display: flex;
              flex-direction: column;
              align-items: center;

              .status-header {
                align-self: flex-start;
              }

              .status-options {
                align-self: flex-start;
              }
            }

            .right-column {
              flex: 1;
              min-width: 200px;
              padding-left: 0;
              display: flex;
              flex-direction: column;
              align-items: center;

              .logo-header {
                align-self: flex-end;
                text-align: right;
                width: 100%;
              }

              .logo-container {
                align-self: flex-end;
              }
            }

            .status-header {
              display: flex;
              align-items: center;
              margin-bottom: 16px;
              border-bottom: 1px solid #ddd;
              justify-content: flex-start;
              width: 100%;
              text-align: left;
              padding-bottom: 8px;
            }

            .logo-header {
              display: flex;
              align-items: center;
              margin-bottom: 16px;
              border-bottom: 1px solid #ddd;
              justify-content: flex-end;
              width: 100%;
              text-align: right;
              padding-bottom: 8px;
            }

            .section-label {
              font-size: 15px;
              font-weight: 500;
              margin-bottom: 0;
              color: #444;
              padding-bottom: 4px;
            }

            // Status options styles
            .status-options {
              width: 100%;
              align-self: flex-start;

              .status-option {
                margin-bottom: 10px;
                text-align: left;

                .status-label {
                  display: block;
                  margin-bottom: 4px;
                  color: #666;
                  font-weight: 500;
                  text-align: left;
                }

                .status-radio-group {
                  display: flex;
                  gap: 20px;

                  .compact-radio {
                    ::ng-deep .mat-radio-label {
                      margin: 0;
                      padding: 0;
                    }

                    ::ng-deep .mat-radio-container {
                      height: 16px;
                      width: 16px;
                    }

                    ::ng-deep .mat-radio-outer-circle,
                    ::ng-deep .mat-radio-inner-circle {
                      height: 16px;
                      width: 16px;
                    }

                    ::ng-deep .mat-radio-label-content {
                      padding-left: 4px;
                      font-size: 13px;
                    }
                  }
                }
              }
            }

            // Logo container styles
            .logo-container {
              display: flex;
              flex-direction: column;
              gap: 10px;
              align-items: center;
              width: 100%;
              max-width: 180px;

              // Interactive logo dropzone
              .logo-dropzone {
                width: 140px;
                height: 140px;
                border-radius: 8px;
                background-color: #f9f9f9;
                position: relative;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                border: 2px dashed #ddd;

                &:hover {
                  border-color: #f8a055;
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                  transform: translateY(-2px);
                }

                &.has-logo {
                  border-style: solid;
                  border-color: #f8a055;
                  position: relative;

                  /* Add a small edit button in the corner that's always visible */
                  &::after {
                    content: 'Edit';
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    background-color: #f8a055;
                    color: white;
                    font-size: 11px;
                    font-weight: 500;
                    padding: 3px 8px;
                    border-top-left-radius: 6px;
                    z-index: 2;
                  }

                  &:hover {
                    border-color: #f8a055;
                  }

                  &:hover .logo-overlay {
                    opacity: 1;
                    background-color: rgba(0, 0, 0, 0.6);
                  }
                }

                // Logo preview
                .logo-preview {
                  width: 100%;
                  height: 100%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  position: relative;

                  img {
                    max-width: 100%;
                    max-height: 100%;
                    object-fit: contain;
                    padding: 8px;
                  }

                  // Edit overlay
                  .logo-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.3);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    opacity: 0; /* Hidden by default, but shown on hover */
                    transition: all 0.2s ease;

                    .overlay-content {
                      display: flex;
                      flex-direction: column;
                      align-items: center;
                      justify-content: center;
                      text-align: center;
                      padding: 10px;

                      mat-icon {
                        color: white;
                        font-size: 24px;
                        width: 24px;
                        height: 24px;
                        margin-bottom: 8px;
                      }

                      .change-text {
                        color: white;
                        font-size: 12px;
                        font-weight: 500;
                        line-height: 1.2;
                      }
                    }
                  }
                }

                // Placeholder
                .logo-placeholder {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  color: #aaa;
                  padding: 16px;
                  text-align: center;

                  mat-icon {
                    font-size: 36px;
                    width: 36px;
                    height: 36px;
                    margin-bottom: 8px;
                    color: #f8a055;
                  }

                  .upload-text {
                    font-size: 13px;
                    color: #666;
                  }
                }

                // Loading state
                .logo-loading {
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;
                  background-color: rgba(255, 255, 255, 0.8);
                  display: flex;
                  align-items: center;
                  justify-content: center;

                  .spinner-border {
                    width: 2rem;
                    height: 2rem;
                    color: #f8a055;
                  }
                }
              }

              // Error message
              .logo-error {
                font-size: 12px;
                text-align: center;
                margin-top: 4px;
              }
            }
          }
        }
      }
    }
  }
}

/* AI Data Section Styles */
.ai-data-section {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
  border-radius: 8px;
  background-color: #fafafa;
  transition: all 0.3s ease;

  .bottomTitles {
    font-weight: 500;
    font-size: 1.2rem;
  }

  /* Common panel styles */
  .ai-intro-panel,
  .ai-processing-panel,
  .ai-complete-panel,
  .ai-error-panel {
    padding: 1.5rem;
    border-radius: 4px;
  }

  .ai-icon {
    color: #673ab7;
    font-size: 24px;
    vertical-align: middle;
    margin-right: 8px;
  }

  /* Start button styling */
  .start-ai-btn {
    padding: 8px 24px;
    font-size: 16px;

    mat-icon {
      margin-right: 8px;
    }
  }

  /* Processing panel styles */
  .ai-processing-panel {
    .processing-title {
      display: flex;
      align-items: center;
      margin-bottom: 1.5rem;

      .processing-icon {
        margin-right: 10px;
        color: #673ab7;
      }

      /* Rotation animation for processing icon */
      .rotating {
        animation: rotate 2s linear infinite;
      }

      @keyframes rotate {
        from {
          transform: rotate(0deg);
        }

        to {
          transform: rotate(360deg);
        }
      }
    }

    .progress-container {
      margin: 1.5rem 0;
      position: relative;

      .progress-label {
        margin-top: 8px;
        text-align: center;
        font-weight: 500;
      }
    }

    .estimated-time {
      display: flex;
      align-items: center;
      color: #666;
      margin-bottom: 1.5rem;

      mat-icon {
        margin-right: 8px;
        font-size: 20px;
      }
    }

    /* Steps styling */
    .processing-steps {
      margin: 2rem 0;

      .step-row {
        display: flex;
        margin-bottom: 1rem;
        padding: 12px;
        border-radius: 4px;
        transition: all 0.3s ease;

        &.completed-step {
          background-color: rgba(76, 175, 80, 0.1);

          .step-status mat-icon {
            color: #4caf50;
          }
        }

        &.active-step {
          background-color: rgba(103, 58, 183, 0.1);
          border-left: 4px solid #673ab7;
        }

        &.pending-step {
          background-color: transparent;
          opacity: 0.7;
        }

        .step-status {
          margin-right: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .step-details {
          flex-grow: 1;

          .step-name {
            font-weight: 500;
            margin-bottom: 4px;
          }

          .step-description {
            color: #666;
            font-size: 14px;
          }
        }
      }
    }

    /* Tips section styling */
    .tips-section {
      margin-top: 2rem;
      padding: 1rem;
      background-color: rgba(33, 150, 243, 0.05);
      border-left: 4px solid #2196f3;
      border-radius: 4px;

      .tip-header {
        display: flex;
        align-items: center;
        color: #2196f3;
        font-weight: 500;
        margin-bottom: 8px;

        mat-icon {
          margin-right: 8px;
        }
      }

      .tip-content {
        color: #555;
        font-style: italic;
      }
    }
  }

  /* Complete panel styles */
  .ai-complete-panel {
    .success-header {
      display: flex;
      align-items: center;
      margin-bottom: 1.5rem;

      .success-icon {
        color: #4caf50;
        margin-right: 12px;
      }

      h3 {
        color: #4caf50;
        margin: 0;
      }
    }

    .success-message {
      margin-bottom: 2rem;
      font-size: 16px;
    }

    /* Download All Container */
    .download-all-container {
      margin-bottom: 2rem;

      .download-all-card {
        background-color: #f8f9fa;
        border: 2px solid #f8a055;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        mat-card-header {
          margin-bottom: 1rem;

          .download-all-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8a055;
            border-radius: 50%;

            mat-icon {
              color: white;
            }
          }

          mat-card-title {
            font-weight: 500;
            color: #333;
          }

          mat-card-subtitle {
            color: #666;
          }
        }

        mat-card-actions {
          padding: 8px 16px 16px;
          display: flex;
          justify-content: center;

          button {
            min-width: 200px;
          }
        }
      }
    }

    .download-options {
      margin-top: 1.5rem;

      .download-card {
        height: 100%;
        transition: transform 0.2s;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
        }

        mat-card-header {
          margin-bottom: 1rem;

          .inventory-icon,
          .packaging-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;
            border-radius: 50%;

            mat-icon {
              color: #673ab7;
            }
          }
        }

        mat-card-content {
          ul {
            padding-left: 20px;

            li {
              margin-bottom: 8px;
              font-size: 14px;
            }
          }
        }

        mat-card-actions {
          padding: 8px 16px 16px;
        }
      }
    }
  }

  /* Error panel styles */
  .ai-error-panel {
    .error-header {
      display: flex;
      align-items: center;
      margin-bottom: 1.5rem;

      .error-icon {
        color: #f44336;
        margin-right: 12px;
      }

      h3 {
        color: #f44336;
        margin: 0;
      }
    }

    .error-message {
      margin-bottom: 2rem;
      font-size: 16px;
    }

    .error-actions {
      text-align: center;
      margin-top: 1rem;
    }
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .account-setup-container {
    .content-section {
      .account-form {
        .compact-form-grid {
          .settings-section {
            .two-column-grid {
              flex-direction: column;
              padding: 0;

              .left-column,
              .right-column {
                width: 100%;
                padding-left: 0;
                padding-right: 0;
                align-items: center;
              }

              .status-options {
                align-items: center;

                .status-option {
                  text-align: center;

                  .status-label {
                    text-align: center;
                  }
                }
              }

              .right-column {
                margin-top: 20px;
              }
            }
          }
        }
      }
    }
  }

  .ai-data-section {
    .download-all-container {
      margin-bottom: 1.5rem;

      .download-all-card {
        mat-card-actions {
          button {
            width: 100%;
          }
        }
      }
    }

    .download-options {
      .col-md-6 {
        margin-bottom: 1rem;
      }
    }

    .ai-intro-panel {
      .start-ai-btn {
        width: 100%;
        margin-top: 1rem;
      }
    }
  }
}

/* Additional utility classes */
.spinner-border-sm {
  width: 20px;
  height: 20px;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/* Main content area loading overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;

  /* Center in the main content area by offsetting from the sidebar */
  @media (min-width: 992px) {
    padding-left: 120px; /* Half of the sidebar width to center in remaining space */
  }

  @media (max-width: 991px) {
    padding-left: 0; /* No offset on mobile when sidebar might be collapsed */
  }
}

.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: white;
  padding: 15px 25px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-right: 10%;
}

.spinner-border {
  width: 2rem;
  height: 2rem;
  border-width: 0.2rem;
  color: #f8a055; /* Mild orange color to match theme */
}

.loading-text {
  margin-top: 0.75rem;
  font-size: 0.9rem;
  color: #666;
  font-weight: 400;
}


.estimated-time {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #ccc;
}

.calculating {
  font-style: italic;
  color: #f7ce2a;
  animation: fadeInOut 1.5s infinite;
}

@keyframes fadeInOut {

  0%,
  100% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }
}

/* AI Data Generation with Tabs Styles */
.ai-data-section {
  margin-top: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.section-icon {
  color: #555;
  margin-right: 10px;
  font-size: 24px;
  height: 24px;
  width: 24px;
}

.section-title {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #333;
}

.chat-bot-section {
  margin: 0;
  overflow: hidden;
  padding: 0 20px 20px;
  background-color: white;
}


.chat-bot-description {
  padding: 15px 20px;
  margin: 0;
  color: #555;
  font-size: 14px;
  border-bottom: 1px solid #e0e0e0;
}

/* Make sure the chat bot has a fixed height */
app-chat-bot {
  display: block;
  height: 550px;
}

/* Dataset tab styling */
.dataset-tab-content {
  padding: 24px;
  min-height: 550px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.dataset-info {
  max-width: 600px;
  text-align: center;
}

.dataset-info h3 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  color: #f8a055; /* Mild orange */
  font-size: 24px;
}

.dataset-info mat-icon {
  margin-right: 10px;
  color: #f8a055; /* Mild orange */
}

.dataset-info p {
  margin-bottom: 16px;
  font-size: 16px;
  line-height: 1.5;
  color: #555;
}

.generate-btn {
  margin-top: 24px;
  padding: 8px 24px;
  font-size: 16px;
}
<div class="smart-dashboard-container">
  <!-- Main Layout -->
  <div class="main-layout">
    <!-- Left Sidebar -->
    <div class="left-sidebar">
      <!-- Dashboard Selection -->
      <div class="dashboard-selection">
        <mat-form-field appearance="outline" class="dashboard-dropdown">
          <mat-label>Select Dashboard</mat-label>
          <mat-select [(value)]="selectedDashboard" [disabled]="!isConfigLoaded" (selectionChange)="onDashboardChange()">
            <mat-option *ngIf="!isConfigLoaded" disabled>Loading...</mat-option>
            <mat-option *ngFor="let dashboardType of dashboardTypes" [value]="dashboardType.value">
              {{dashboardType.label}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <!-- Smart Filters Section -->
      <div class="filters-section">
        <h3 class="filters-title">
          <mat-icon>tune</mat-icon>
          Smart Filters
        </h3>

        <!-- Restaurants Filter -->
        <div class="filter-group">
          <h4 class="filter-label">
            <mat-icon>restaurant</mat-icon>
            Restaurants
          </h4>
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Select restaurants</mat-label>
            <mat-select [formControl]="selectedLocationsCtrl" multiple>
              <mat-option>
                <ngx-mat-select-search
                  [formControl]="locationFilterCtrl"
                  placeholderLabel="Search locations..."
                  noEntriesFoundLabel="No locations found">
                </ngx-mat-select-search>
              </mat-option>

              <mat-option *ngFor="let branch of filteredBranches" [value]="branch.restaurantIdOld">
                {{branch.branchName}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- Base Date Filter - Only for Purchase Dashboard -->
        <div class="filter-group" *ngIf="selectedDashboard === 'purchase'">
          <h4 class="filter-label">
            <mat-icon>event</mat-icon>
            Base Date
          </h4>
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Select base date</mat-label>
            <mat-select [formControl]="baseDateCtrl" [disabled]="!isConfigLoaded">
              <mat-option *ngIf="!isConfigLoaded" disabled>Loading...</mat-option>
              <mat-option *ngFor="let baseDateOption of baseDateOptions" [value]="baseDateOption.value">
                {{baseDateOption.label}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <!-- Start Date Filter -->
        <div class="filter-group">
          <h4 class="filter-label">
            <mat-icon>date_range</mat-icon>
            Start Date
          </h4>
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Start Date</mat-label>
            <input matInput [matDatepicker]="startPicker" [formControl]="startDate">
            <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
            <mat-datepicker #startPicker></mat-datepicker>
          </mat-form-field>
        </div>

        <!-- End Date Filter -->
        <div class="filter-group">
          <h4 class="filter-label">
            <mat-icon>date_range</mat-icon>
            End Date
          </h4>
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>End Date</mat-label>
            <input matInput [matDatepicker]="endPicker" [formControl]="endDate">
            <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
            <mat-datepicker #endPicker></mat-datepicker>
          </mat-form-field>
        </div>

        <!-- Filter Action Buttons -->
        <div class="filter-actions">
          <button mat-stroked-button class="reset-filters-btn" (click)="resetFilters()">
            <mat-icon>refresh</mat-icon>
            Reset
          </button>
          <button mat-stroked-button color="primary" class="search-btn" (click)="searchDashboard()">
            <mat-icon>search</mat-icon>
            Search
          </button>
        </div>
      </div>

      <!-- Dashboard Mode Toggle Switch - At Very Bottom -->
      <div class="dashboard-mode-section">
        <h4 class="mode-label">Dashboard Mode</h4>
        <div class="toggle-switch-container">
          <div class="toggle-switch" [class.ai-mode]="dashboardModeCtrl.value === 'ask_digi_ai'">
            <div class="toggle-option"
                 [class.active]="dashboardModeCtrl.value === 'default'"
                 (click)="setDashboardMode('default')">
              Default
            </div>
            <div class="toggle-option ai-option"
                 [class.active]="dashboardModeCtrl.value === 'ask_digi_ai'"
                 [class.disabled]="true"
                 [matTooltip]="'AI Assistant - Available Soon'"
                 (click)="setDashboardMode('ask_digi_ai')">
              Ask Digi AI
            </div>
            <div class="toggle-slider" [class.ai-position]="dashboardModeCtrl.value === 'ask_digi_ai'"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Content Area -->
    <div class="right-content">
      <!-- Top Search Bar -->
      <div class="search-header">
        <div class="assistant-info">
          <mat-icon class="assistant-icon" [class.disabled]="isSearchBarDisabled()">smart_toy</mat-icon>
          <div class="assistant-text">
            <span class="assistant-title">Smart Dashboard Assistant</span>
            <span class="assistant-status" [class.disabled]="isSearchBarDisabled()">
              {{isSearchBarDisabled() ? 'Available Soon' : 'Ready to analyze'}}
            </span>
          </div>
        </div>
        <div class="search-container">
          <mat-form-field appearance="outline" class="search-field" [class.disabled]="isSearchBarDisabled()">
            <input matInput
                   placeholder="Ask me about your business data (Available Soon)"
                   [formControl]="searchQuery"
                   [disabled]="isSearchBarDisabled()"
                   (keyup.enter)="onSearchQuery()" />
            <mat-icon matSuffix class="search-icon"
                      [class.disabled]="isSearchBarDisabled()"
                      (click)="onSearchQuery()">search</mat-icon>
          </mat-form-field>
        </div>
      </div>

      <!-- Dashboard Content Area -->
      <div class="dashboard-content-area">
        <!-- Loading Spinner -->
        <div *ngIf="isLoading" class="loading-container">
          <mat-spinner diameter="50"></mat-spinner>
          <p>Loading dashboard data...</p>
        </div>

        <!-- Dashboard Grid -->
        <div *ngIf="!isLoading && (summaryCards.length > 0 || charts.length > 0)" class="dashboard-grid">
          <!-- Summary Cards Row -->
          <div *ngIf="summaryCards.length > 0" class="summary-cards-row">
            <mat-card *ngFor="let card of summaryCards" class="summary-card" [style.border-left-color]="card.color">
              <mat-card-content>
                <div class="card-content">
                  <div class="card-icon" [style.color]="card.color">
                    <mat-icon>{{card.icon}}</mat-icon>
                  </div>
                  <div class="card-info">
                    <div class="card-value">{{card.value}}</div>
                    <div class="card-label">{{card.label}}</div>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>

          <!-- Charts Grid -->
          <div *ngIf="charts.length > 0" class="charts-grid">
            <mat-card *ngFor="let chart of charts; let i = index"
                      class="chart-card"
                      [ngClass]="getChartCssClass(chart)">
              <mat-card-header>
                <mat-card-title class="chart-title">{{chart.title}}</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="chart-container" [attr.data-chart-type]="chart.type">
                  <!-- Dynamic Chart Rendering -->
                  <canvas baseChart
                          [type]="getChartType(chart)"
                          [data]="getChartData(chart)"
                          [options]="getChartOptions(chart)">
                  </canvas>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </div>

        <!-- Single Unified Empty State -->
        <div *ngIf="!isLoading && summaryCards.length === 0 && charts.length === 0" class="empty-state">
          <mat-icon class="empty-icon">analytics</mat-icon>
          <h3>No Data Available</h3>
          <p>Please select filters and click the Search button to view dashboard data.</p>
          <button mat-raised-button color="primary" (click)="searchDashboard()">
            <mat-icon>refresh</mat-icon>
            Refresh Data
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

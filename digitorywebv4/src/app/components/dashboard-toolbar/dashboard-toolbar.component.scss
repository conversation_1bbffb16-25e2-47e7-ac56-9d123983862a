mat-toolbar {
  background: linear-gradient(135deg, #ffffff 0%, #fff5f0 50%, #fff2e6 100%); /* White to light orange gradient */
  color: #333;
  padding: 0 16px; /* Increased padding for better spacing */
  height: 40px; /* Reduced height */
  min-height: 40px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(255, 179, 102, 0.15); /* Light orange-tinted shadow */
  border-bottom: 1px solid rgba(255, 179, 102, 0.2); /* Light orange bottom border */
}

.toolbar-left {
  display: flex;
  align-items: center;
  flex: 1;
  padding-left: 0;
}

.nav-menu {
  display: flex;
  align-items: center;
  margin-left: 0; /* No margin needed without logo */

  .nav-item {
    padding: 0 16px; /* Increased padding for better spacing */
    height: 40px; /* Match new toolbar height */
    display: flex;
    align-items: center;
    border-radius: 0;
    color: #333;
    transition: all 0.3s ease;
    position: relative;
    margin: 0 2px; /* Slightly increased spacing between tabs */

    &:first-child {
      padding-left: 16px; /* Consistent padding without logo */
    }

    /* Fix for mat-button padding */
    ::ng-deep .mat-mdc-button-touch-target {
      height: 100%;
    }

    mat-icon {
      margin-right: 8px;
      color: #ff9100; /* Orange icon color */
    }

    &:hover {
      background: linear-gradient(135deg, rgba(255, 145, 0, 0.08) 0%, rgba(255, 145, 0, 0.12) 100%);
      transform: translateY(-1px);
    }

    &.active {
      font-weight: 500;
      background: linear-gradient(135deg, rgba(255, 179, 102, 0.1) 0%, rgba(255, 179, 102, 0.15) 100%);

      /* Add light orange underline with gradient */
      &::after {
        content: '';
        position: absolute;
        bottom: -1px; /* Align with bottom border of toolbar */
        left: 0;
        width: 100%;
        height: 3px; /* Slightly thicker for better visibility */
        background: linear-gradient(90deg, #ffb366 0%, #ffa64d 100%);
      }
    }

    /* Placeholder tab styling with gradient */
    &.placeholder-tab {
      opacity: 0.7;
      cursor: default;
      pointer-events: none;
      background: linear-gradient(135deg, rgba(255, 179, 102, 0.03) 0%, rgba(255, 179, 102, 0.06) 100%);

      mat-icon {
        color: rgba(255, 179, 102, 0.5);
      }

      span {
        color: #666;
      }
    }
  }
}

/* Category navigation removed */

.example-spacer {
  flex: 1 1 auto;
}

.icons {
  display: flex;
  align-items: center;
  gap: 20px;
}

::ng-deep .mat-toolbar-row, .mat-toolbar-single-row {
    padding: 0 !important;
    height: 40px !important;
    max-height: 40px !important;
}

.user-info {
  display: flex;
  align-items: center;
  margin-left: 12px;
}

.user-menu-button {
  display: flex;
  align-items: center;
  padding: 0 10px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  height: 42px;

  &:hover {
    background-color: rgba(255, 145, 0, 0.05);
  }

  mat-icon {
    margin-right: 8px;
    font-size: 24px;
    height: 24px;
    width: 24px;
    color: #ff9100; /* Orange icon color */
  }
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  line-height: 1.2;
}

.user-name {
  font-size: 14px; /* Increased font size */
  font-weight: 500;
}

.user-role {
  font-size: 12px; /* Increased font size */
  opacity: 0.8;
}

// .menu_buttons {
//   min-width: auto !important;
//   line-height: 36px;
//   // padding: 0 !important;
//   // margin: 0 10px !important;
// }

// .menu_buttons2 {
//   margin: 0 24px !important;
// }

.formal-text {
  font-family: Arial, sans-serif;
  font-size: 14px; /* Increased font size */
  text-align: right;
  line-height: 1.5;
  margin: 0 12px;
  color: #333;
  white-space: nowrap;
}

.beta-tag {
  color: #ff9100;
  font-weight: bold;
  margin-left: 3px;
  font-size: 12px; /* Increased font size */
  background-color: rgba(255, 145, 0, 0.1);
  padding: 2px 6px; /* Increased padding */
  border-radius: 3px;
}

.mat-mdc-button>.mat-icon {
  overflow: visible !important;
}

.globalSelectFormField{
  ::ng-deep .mat-mdc-form-field-subscript-wrapper {
    display: none !important;
  }
}

.globalSelectFormField{
  ::ng-deep .mdc-text-field--filled:not(.mdc-text-field--disabled) {
    background-color: #ebebeb;
}
}

.globalSelectFormField{
  width: 215px;
  height: 45px;
  margin-bottom: 10px;
  margin-right: 5px;
}

.globalSelectInput{
  height: 30px;
  padding: 10px;
}

.mdc-text-field--no-label:not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mat-mdc-form-field-infix {
  padding-top: 14px;
  padding-bottom: 16px;
}
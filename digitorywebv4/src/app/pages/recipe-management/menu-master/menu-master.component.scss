.instructionsIcon {
  margin-right: 7px;
}

.topTitleName {
  display: inline-flex;
  font-size: large;
  font-weight: bold;
}

.restIcons {
  margin-right: 10px;
}

.closeBtnMapping {
  margin-left: 5px;
}

.dialog-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  margin: auto;
  border: 1px solid #e0e0e0;
  position: relative;
}

.close-btn {
  display: flex;
  justify-content: flex-end;
}


.close-btn-icon {
  color: #ff0000;
}

.registration-form {
  font-family: 'Roboto', sans-serif;
  color: #333;
}


.portion-info {
  margin-top: 20px;
  padding: 10px;
  border-top: 1px solid #e0e0e0;
}

.info-item {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.info-content {
  margin: 0;
  font-size: 1.1em;
  color: #004175;
}

.info-content strong {
  color: #004175;
}

.golden-yellow-chip {
  background-color: #ffc107 !important;
  color: rgba(0, 0, 0, 0.87) !important;
  max-height: 1.2rem;
}

.tableIcons {
  margin-right: 10px;
  overflow: visible !important;
}

.tableModName {
  color: grey;
}

.sub-recipe {
  color: orange;
}


.portion-info {
  margin-top: 10px;
}

.info-table {
  width: 100%;
  border-collapse: collapse;
}

.info-table td {
  padding: 8px;
  border-bottom: 1px solid #ddd;
}

.info-key {
  text-align: left;
  font-weight: bold;
}

.info-value {
  text-align: right;
  color: #333;
}

.bottom-titles {
  color: rgba(0, 0, 0, 0.6);
  font-size: larger;
  font-weight: bolder;
  background-color: #e5e5e5;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.flex-item {
  flex: 1 1 calc(16.66% - 16px);
  min-width: 150px;
}

.discontinue-row {
  display: flex;
  align-items: center;
  gap: 16px;
}





.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin: 5px;
  gap: 10px;
}



.topTitleName {
  display: flex;
  align-items: center;
  font-size: 1.2rem;
  font-weight: bold;
}

.topTitleName .mat-icon {
  margin-right: 8px;
  vertical-align: middle;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  gap: 10px;
}

.button-group button {
  min-width: 100px;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-group .mat-icon {
  margin-right: 5px;
}

.spinner-border {
  width: 1rem;
  height: 1rem;
  margin-right: 5px;
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    align-items: stretch;
  }

  .topTitleName {
    text-align: center;
    margin-bottom: 10px;
  }

  .button-group {
    flex-direction: column;
  }

  .button-group button {
    width: 100%;
  }
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: -0.5rem;
}

.col {
  flex: 1 0 100%;
  padding: 0.5rem;
}

@media (min-width: 576px) {
  .col {
    flex: 0 0 50%;
  }
}

@media (min-width: 768px) {
  .col {
    flex: 0 0 33.333%;
  }
}

@media (min-width: 992px) {
  .col {
    flex: 0 0 16.666%;
  }
}

.topCards {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  padding: 0.5rem;
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.topCards:hover {
  transform: translateY(-5px);
}

.topCardText {
  font-size: 1rem;
  font-weight: bold;
  // margin-bottom: 0.5rem;
}

.topCardsCircle {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ab {
  font-size: 1rem;
  font-weight: bold;
}

.circleIcons,.circleIconsPer {
  font-size: 1.2rem;
}

.sellingInput {
  width: 70%;
  padding: 0.25rem;
  border: 1px solid #ccc;
  border-radius: 4px;
}

@use '../../../../styles/variables' as vars;

/* Color classes for different card types - Orange-based theme */
.topCardStorage { background-color: map-get(vars.$semantic-colors, info);}
.topCardmonetization_on { background-color: map-get(vars.$semantic-colors, positive);}
.topCardshopping_basket { background-color: vars.$orange-lightest;}
.topCardaccount_balance_wallet { background-color: #fff2f2;}
.topCardaccount-percentage { background-color: #f5f0f8;}
.topCardaccount-ProPpercentage { background-color: #f8f6f4;}

.chip-color{
  background-color: vars.$orange-primary !important;
}

.selling-price-hover {
  cursor: pointer;
}

.info-unit {
  text-align: right;
  color: vars.$warm-gray; /* Orange-themed color for units */
}

.hi-badge {
  background: linear-gradient(45deg, #e7cd0d, #d1c647);
  color: rgb(255, 255, 255);
  font-size: 0.8rem;
  padding: 3px 10px;
  border-radius: 12px;
  display: inline-flex;
  align-items: center;
  line-height: 1.55;
}

.tab-1{
  font-size: 1rem; font-weight: 600; color: #424242;
}

.cardValues{
  margin-top: -5px;
  font-size: small;
}

.cardSelect{

  height: 24px !important;
  width: 160px !important;

  ::ng-deep .mat-mdc-form-field-subscript-wrapper {
    display: none !important;
  }

  ::ng-deep .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix {
    padding-top: 5px !important;
  }

  ::ng-deep .mdc-text-field--filled:not(.mdc-text-field--disabled) {
    background-color: transparent !important;
    padding: 0px !important;
    margin-bottom: 4px !important;
  }

  ::ng-deep .mdc-text-field--no-label:not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mat-mdc-form-field-infix {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
  }

  ::ng-deep .mdc-line-ripple {
    display: none !important;
  }
}

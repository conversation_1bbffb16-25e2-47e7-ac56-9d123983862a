import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface DashboardFilters {
  locations: string[];
  startDate: string;
  endDate: string;
  baseDate: string;
}

export interface SmartDashboardRequest {
  tenant_id: string;
  filters: DashboardFilters;
  user_query: string;
  use_default_charts: boolean;
  dashboard_type: string;
}

export interface SummaryItem {
  icon: string;
  value: string;
  label: string;
  data_type: string;
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor: string[];
  borderColor: string[];
}

export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface Chart {
  id: string;
  title: string;
  type: string;
  data: ChartData;
}

export interface DashboardResponse {
  charts: Chart[];
  summary_items: SummaryItem[];
}

export interface SmartDashboardApiResponse {
  status: string;
  data: DashboardResponse;
}



@Injectable({
  providedIn: 'root'
})
export class SmartDashboardService {
  private baseUrl: string = environment.engineUrl;

  constructor(private http: HttpClient) {}

  /**
   * Get smart dashboard data with filters and optional query
   */
  getSmartDashboardData(request: SmartDashboardRequest): Observable<SmartDashboardApiResponse> {
    return this.http.post<SmartDashboardApiResponse>(`${this.baseUrl}api/smart-dashboard/smart_ask`, request);
  }

  /**
   * Format currency value
   */
  formatCurrency(value: number, currency: string = '₹'): string {
    if (value >= 10000000) { // 1 crore
      return `${currency}${(value / 10000000).toFixed(2)}Cr`;
    } else if (value >= 100000) { // 1 lakh
      return `${currency}${(value / 100000).toFixed(2)}L`;
    } else if (value >= 1000) { // 1 thousand
      return `${currency}${(value / 1000).toFixed(2)}K`;
    } else {
      return `${currency}${value.toFixed(2)}`;
    }
  }

  /**
   * Format number with Indian numbering system
   */
  formatNumber(value: number): string {
    if (value >= 10000000) { // 1 crore
      return `${(value / 10000000).toFixed(2)}Cr`;
    } else if (value >= 100000) { // 1 lakh
      return `${(value / 100000).toFixed(2)}L`;
    } else if (value >= 1000) { // 1 thousand
      return `${(value / 1000).toFixed(2)}K`;
    } else {
      return value.toString();
    }
  }

  /**
   * Process chart data from backend response
   * @deprecated Use ChartRendererService instead
   */
  processChartData(chartData: any): any {
    // Simply return the data as-is since backend now provides complete configuration
    return chartData.data || chartData;
  }



  /**
   * Get icon for summary card based on data type
   */
  getSummaryCardIcon(dataType: string, label: string): string {
    const iconMap: { [key: string]: string } = {
      'currency': 'attach_money',
      'number': 'numbers',
      'percentage': 'percent',
      'vendor': 'store',
      'ratio': 'autorenew'
    };

    // Check label for specific icons
    if (label.toLowerCase().includes('order')) {
      return 'shopping_cart';
    } else if (label.toLowerCase().includes('amount') || label.toLowerCase().includes('value')) {
      return 'attach_money';
    } else if (label.toLowerCase().includes('vendor') || label.toLowerCase().includes('supplier')) {
      return 'store';
    } else if (label.toLowerCase().includes('average')) {
      return 'trending_up';
    } else if (label.toLowerCase().includes('inventory') && label.toLowerCase().includes('items')) {
      return 'inventory';
    } else if (label.toLowerCase().includes('stockout')) {
      return 'warning';
    } else if (label.toLowerCase().includes('turnover')) {
      return 'autorenew';
    } else if (label.toLowerCase().includes('variance')) {
      return 'trending_down';
    } else if (label.toLowerCase().includes('issued')) {
      return 'sync_alt';
    }

    return iconMap[dataType] || 'analytics';
  }

  /**
   * Validate date range
   */
  validateDateRange(startDate: Date, endDate: Date): boolean {
    if (!startDate || !endDate) {
      return false;
    }

    // End date should be after start date
    if (endDate <= startDate) {
      return false;
    }

    // Date range should not be more than 1 year
    const oneYear = 365 * 24 * 60 * 60 * 1000;
    if (endDate.getTime() - startDate.getTime() > oneYear) {
      return false;
    }

    return true;
  }

  /**
   * Get default date range (last 30 days)
   */
  getDefaultDateRange(): { startDate: Date; endDate: Date } {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    return { startDate, endDate };
  }
}

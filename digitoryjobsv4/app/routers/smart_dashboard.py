"""
Smart Dashboard Router - LLM-powered dashboard generation
"""
from fastapi import API<PERSON>outer, Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, Any
import os
from datetime import datetime
from app.utility.report import grnStatusReport, store_variance
from app.utility.dashboard_agents import smart_ask_dashboard, generate_purchase_dashboard, generate_inventory_dashboard
from dotenv import load_dotenv
load_dotenv()

router = APIRouter()
security = HTTPBearer()

async def authenticate(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if not credentials:
        raise HTTPException(status_code=401, detail="Not authenticated")
    expected_token = os.getenv("BEARER_TOKEN")
    if credentials.credentials != expected_token:
        raise HTTPException(status_code=401, detail="Invalid token")
    return credentials.credentials

@router.get("/config")
async def get_dashboard_config(_: str = Depends(authenticate)):
    """
    Get global dashboard configuration for dynamic frontend rendering
    """
    return {
        "status": "success",
        "data": {
            "chart_colors": [
                '#ff8c42',  # Orange primary
                '#ffb366',  # Orange light
                '#87a96b',  # Sage green
                '#6b9bd2',  # Soft blue
                '#9b7bb8',  # Muted purple
                '#8d7b68',  # Warm gray
                '#d4a5a5',  # Dusty rose
                '#ffc999',  # Orange lighter
                '#a4c085',  # Sage green light
                '#85aedb',  # Soft blue light
                '#af95c6',  # Muted purple light
                '#a69082',  # Warm gray light
                '#ddb8b8',  # Dusty rose light
                '#ffe0cc',  # Orange lightest
                '#f4a261'   # Warning orange
            ],
            "chart_types": {
                "bar": "Bar Chart",
                "horizontalBar": "Horizontal Bar Chart",
                "line": "Line Chart",
                "doughnut": "Doughnut Chart",
                "pie": "Pie Chart",
                "radar": "Radar Chart",
                "polarArea": "Polar Area Chart"
            },
            "currency": {"code": "INR", "symbol": "₹"},
            "dashboard_types": [
                {"value": "inventory", "label": "Inventory Dashboard"},
                {"value": "purchase", "label": "Purchase Dashboard"},
            ],
            "base_date_options": [
                {"value": "deliveryDate", "label": "Delivery Date"},
                {"value": "orderDate", "label": "Order Date"},
                {"value": "createdDate", "label": "Created Date"}
            ],
            "default_chart_options": {
                "responsive": True,
                "maintainAspectRatio": False,
                "plugins": {
                    "legend": {
                        "display": True,
                        "position": "top",
                        "labels": {
                            "usePointStyle": True,
                            "padding": 15,
                            "font": {"size": 11}
                        }
                    },
                    "tooltip": {
                        "backgroundColor": "rgba(255, 255, 255, 0.95)",
                        "titleColor": "#333",
                        "bodyColor": "#666",
                        "borderColor": "#ffb366",
                        "borderWidth": 2,
                        "cornerRadius": 6
                    }
                },
                "scales": {
                    "x": {
                        "grid": {"display": False},
                        "ticks": {"font": {"size": 10}}
                    },
                    "y": {
                        "beginAtZero": True,
                        "grid": {"color": "#e9ecef"},
                        "ticks": {"font": {"size": 10}}
                    }
                }
            },
            "summary_card_config": {
                "colors": {
                    "currency": "#ffb366",
                    "number": "#ff9d4d",
                    "percentage": "#ffc999",
                    "text": "#6c757d"
                },
                "icons": {
                    "currency": "account_balance_wallet",
                    "number": "analytics",
                    "percentage": "percent",
                    "text": "info"
                }
            },
            "ui_config": {
                "default_date_range_days": 30,
                "default_dashboard_type": "inventory",
                "default_base_date": "deliveryDate"
            }
        }
    }

@router.post("/smart_ask")
async def smart_ask(request: Dict[str, Any], _: str = Depends(authenticate)):
    filters = request.get('filters', {})
    user_query = request.get('user_query', '')
    tenant_id = request.get('tenant_id', '')
    use_default_charts = request.get('use_default_charts', False)
    dashboard_type = request.get('dashboard_type', 'purchase')

    job = {
        'tenantId': tenant_id,
        'details': {
            'selectedRestaurants': filters.get('locations', []),
            'selectedBaseDate': filters.get('baseDate', 'deliveryDate'),
            'startDate': datetime.strptime(filters.get('startDate'), '%Y-%m-%d'),
            'endDate': datetime.strptime(filters.get('endDate'), '%Y-%m-%d')
        }
    }
    
    if dashboard_type == 'purchase':
        df = grnStatusReport(job)
        dashboard_data = generate_purchase_dashboard(df)
    elif dashboard_type == 'inventory':
        job['details']['selectedCategories'] = ['all']
        job['details']['selectedSubCategories'] = ['all']
        job['details']['selectedVendors'] = []
        job['details']['selectedWorkAreas'] = []
        job['details']['type'] = 'store_variance'
        df = store_variance(job)
        dashboard_data = generate_inventory_dashboard(df)

    else:
        # Default GRN dashboard
        df = grnStatusReport(job)
        dashboard_data = smart_ask_dashboard(df, user_query, use_default_charts)

    return {"status": "success", "data": dashboard_data}



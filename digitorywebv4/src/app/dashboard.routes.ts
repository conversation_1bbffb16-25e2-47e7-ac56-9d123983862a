import { Routes } from '@angular/router';
import { routesConfig } from './config/routes';
export const dashboardroutes: Routes = [
  {
    path: routesConfig.dashboard.childRoutes.home.routerPath,
    loadComponent: () =>
      import('./pages/dashboard-home/dashboard-home.component').then(
        (c) => c.DashboardHomeComponent
      ),
  },
  {
    path: routesConfig.dashboard.childRoutes.setting.routerPath,
    loadComponent: () =>
      import('./pages/dashboard-profile/dashboard-profile.component').then(
        (c) => c.DashboardProfileComponent
      ),
  },

  {
    path: routesConfig.dashboard.childRoutes.inventory.routerPath,
    loadComponent: () =>
      import('./pages/inventory-management/master-data.component').then(
        (c) => c.MasterDataComponent
      ),
  },
  {
    path: routesConfig.dashboard.childRoutes.user.routerPath,
    loadComponent: () =>
    import('./pages/user-management/parent/parent.component').then(
      (c) => c.ParentComponent
    )
  },

  {
    path: routesConfig.dashboard.childRoutes.recipe.routerPath,
    loadComponent: () =>
    import('./pages/recipe-management/parent/parent.component').then(
      (c) => c.ParentComponent
    )
  },
  {
    path: routesConfig.dashboard.childRoutes.account.routerPath,
    loadComponent: () =>
    import('./pages/crm-management/parent/parent.component').then(
      (c) => c.ParentComponent
    )
  },
  {
    path: routesConfig.dashboard.childRoutes.accountSetup.routerPath,
    loadComponent: () =>
    import('./pages/crm-management/account-setup/account-setup.component').then(
      (c) => c.AccountSetupComponent
    )
  },
  // {
  //   path: routesConfig.dashboard.childRoutes.createParty.routerPath,
  //   loadComponent: () =>
  //   import('./pages/party-management/create-party/create-party.component').then(
  //     (c) => c.CreatePartyComponent
  //   )
  // },
  {
    path: routesConfig.dashboard.childRoutes.party.routerPath,
    loadComponent: () =>
    import('./pages/party-management/parent/parent.component').then(
      (c) => c.ParentComponent
    )
  },
  {
    path: routesConfig.dashboard.childRoutes.smartDashboard.routerPath,
    loadComponent: () =>
      import('./pages/smart-dashboard/smart-dashboard.component').then(
        (c) => c.SmartDashboardComponent
      ),
  },
  // {
  //   path: routesConfig.dashboard.childRoutes.figma.routerPath,
  //   loadComponent: () =>
  //   import('./pages/figma-component/figma-component.component').then(
  //     (c) => c.FigmaComponentComponent
  //   )
  // },
  // {
  //   path: routesConfig.dashboard.childRoutes.unauthorized.routerPath,
  //   loadComponent: () =>
  //   import('./pages/unauthorized/unauthorized.component').then(
  //     (c) => c.UnauthorizedComponent
  //   )
  // },
  {
    path: '',
    pathMatch: 'full',
    redirectTo: routesConfig.dashboard.childRoutes.home.routerPath,
  },
];
